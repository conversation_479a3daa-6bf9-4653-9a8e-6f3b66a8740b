@echo off
chcp 65001 >nul
title 汇易达保单监控系统启动脚本

echo.
echo ========================================
echo   汇易达保单监控系统启动脚本
echo   版本: v3.3.11
echo   日期: 2025-08-19
echo ========================================
echo.

set BACKEND_JAR=ruoyi-admin.jar
set FRONTEND_DIR=ruoyi-ui
set BACKEND_PORT=8080
set FRONTEND_PORT=80

echo [1] 启动后端服务 (端口: %BACKEND_PORT%)
echo [2] 启动前端服务 (端口: %FRONTEND_PORT%)
echo [3] 启动全部服务 (前端 + 后端)
echo [4] 停止后端服务
echo [5] 停止前端服务
echo [6] 停止全部服务
echo [7] 重启全部服务
echo [8] 查看服务状态
echo [9] 构建后端项目
echo [10] 构建前端项目 (生产环境)
echo [11] 构建前端项目 (预发布环境)
echo [12] 构建全部项目 (前端 + 后端)
echo [0] 退出
echo.

set /p choice=请选择操作 (0-9,A-C):

if "%choice%"=="1" goto start_backend
if "%choice%"=="2" goto start_frontend
if "%choice%"=="3" goto start_all
if "%choice%"=="4" goto stop_backend
if "%choice%"=="5" goto stop_frontend
if "%choice%"=="6" goto stop_all
if "%choice%"=="7" goto restart_all
if "%choice%"=="8" goto status
if "%choice%"=="9" goto build_backend
if /i "%choice%"=="10" goto build_frontend_prod
if /i "%choice%"=="11" goto build_frontend_stage
if /i "%choice%"=="12" goto build_all
if "%choice%"=="0" goto exit
echo 无效选择，请重新运行脚本
pause
goto exit

:start_backend
echo.
echo ========================================
echo 启动后端服务...
echo ========================================
if not exist %BACKEND_JAR% (
    echo 错误: %BACKEND_JAR% 文件不存在！
    echo 请先执行构建操作 [9]
    pause
    goto exit
)

for /f "tokens=1" %%i in ('jps -l ^| findstr %BACKEND_JAR%') do (
    echo 后端服务已在运行 (PID: %%i)
    echo 访问地址: http://localhost:%BACKEND_PORT%
    pause
    goto exit
)

echo 正在启动后端服务...
start "汇易达后端服务" javaw -Dname=%BACKEND_JAR% -Duser.timezone=Asia/Shanghai -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -jar %BACKEND_JAR%

timeout /t 5 /nobreak >nul
echo 后端服务启动完成！
echo 访问地址: http://localhost:%BACKEND_PORT%
echo Swagger文档: http://localhost:%BACKEND_PORT%/swagger-ui.html
echo Druid监控: http://localhost:%BACKEND_PORT%/druid/
pause
goto exit

:start_frontend
echo.
echo ========================================
echo 启动前端服务...
echo ========================================
if not exist %FRONTEND_DIR% (
    echo 错误: %FRONTEND_DIR% 目录不存在！
    pause
    goto exit
)

cd %FRONTEND_DIR%

echo 检查依赖...
if not exist node_modules (
    echo 正在安装依赖...
    call npm install
    if errorlevel 1 (
        echo 依赖安装失败！
        pause
        cd ..
        goto exit
    )
)

echo 正在启动前端开发服务器...
start "汇易达前端服务" cmd /k "npm run dev"

cd ..
timeout /t 3 /nobreak >nul
echo 前端服务启动完成！
echo 访问地址: http://localhost:%FRONTEND_PORT%
pause
goto exit

:start_all
echo.
echo ========================================
echo 启动全部服务...
echo ========================================
call :start_backend_silent
timeout /t 8 /nobreak >nul
call :start_frontend_silent
echo.
echo 全部服务启动完成！
echo 前端访问地址: http://localhost:%FRONTEND_PORT%
echo 后端访问地址: http://localhost:%BACKEND_PORT%
pause
goto exit

:start_backend_silent
if not exist %BACKEND_JAR% (
    echo 错误: %BACKEND_JAR% 文件不存在！请先构建项目
    exit /b 1
)

for /f "tokens=1" %%i in ('jps -l ^| findstr %BACKEND_JAR% 2^>nul') do (
    echo 后端服务已在运行
    exit /b 0
)

echo 启动后端服务...
start "汇易达后端服务" javaw -Dname=%BACKEND_JAR% -Duser.timezone=Asia/Shanghai -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -jar %BACKEND_JAR%
exit /b 0

:start_frontend_silent
if not exist %FRONTEND_DIR% (
    echo 错误: %FRONTEND_DIR% 目录不存在！
    exit /b 1
)

cd %FRONTEND_DIR%
if not exist node_modules (
    echo 安装前端依赖...
    call npm install
)
echo 启动前端服务...
start "汇易达前端服务" cmd /k "npm run dev"
cd ..
exit /b 0

:stop_backend
echo.
echo ========================================
echo 停止后端服务...
echo ========================================
for /f "tokens=1" %%i in ('jps -l ^| findstr %BACKEND_JAR% 2^>nul') do (
    echo 正在停止后端服务 (PID: %%i)
    taskkill /f /pid %%i
    echo 后端服务已停止
    pause
    goto exit
)
echo 后端服务未运行
pause
goto exit

:stop_frontend
echo.
echo ========================================
echo 停止前端服务...
echo ========================================
for /f "tokens=2" %%i in ('netstat -ano ^| findstr :%FRONTEND_PORT% ^| findstr LISTENING') do (
    echo 正在停止前端服务 (PID: %%i)
    taskkill /f /pid %%i
)
echo 前端服务已停止
pause
goto exit

:stop_all
echo.
echo ========================================
echo 停止全部服务...
echo ========================================
call :stop_backend_silent
call :stop_frontend_silent
echo 全部服务已停止
pause
goto exit

:stop_backend_silent
for /f "tokens=1" %%i in ('jps -l ^| findstr %BACKEND_JAR% 2^>nul') do (
    echo 停止后端服务 (PID: %%i)
    taskkill /f /pid %%i
)
exit /b 0

:stop_frontend_silent
for /f "tokens=2" %%i in ('netstat -ano ^| findstr :%FRONTEND_PORT% ^| findstr LISTENING 2^>nul') do (
    echo 停止前端服务 (PID: %%i)
    taskkill /f /pid %%i
)
exit /b 0

:restart_all
echo.
echo ========================================
echo 重启全部服务...
echo ========================================
call :stop_all_silent
timeout /t 3 /nobreak >nul
call :start_backend_silent
timeout /t 8 /nobreak >nul
call :start_frontend_silent
echo 全部服务重启完成！
pause
goto exit

:stop_all_silent
call :stop_backend_silent
call :stop_frontend_silent
exit /b 0

:status
echo.
echo ========================================
echo 服务状态检查...
echo ========================================
echo.

echo 后端服务状态:
for /f "tokens=1,2" %%i in ('jps -l ^| findstr %BACKEND_JAR% 2^>nul') do (
    echo   ✓ 运行中 (PID: %%i)
    echo   ✓ 访问地址: http://localhost:%BACKEND_PORT%
    goto check_frontend
)
echo   ✗ 未运行

:check_frontend
echo.
echo 前端服务状态:
for /f "tokens=2" %%i in ('netstat -ano ^| findstr :%FRONTEND_PORT% ^| findstr LISTENING 2^>nul') do (
    echo   ✓ 运行中 (PID: %%i)
    echo   ✓ 访问地址: http://localhost:%FRONTEND_PORT%
    goto status_end
)
echo   ✗ 未运行

:status_end
echo.
pause
goto exit

:build_backend
echo.
echo ========================================
echo 构建后端项目...
echo ========================================
echo 正在执行 Maven 构建...
call mvn clean package -Dmaven.test.skip=true

if errorlevel 1 (
    echo 构建失败！
    pause
    goto exit
)

if exist ruoyi-admin\target\ruoyi-admin.jar (
    copy ruoyi-admin\target\ruoyi-admin.jar . >nul
    echo 构建成功！JAR文件已复制到根目录
) else (
    echo 构建失败！未找到JAR文件
)
pause
goto exit

:build_frontend_prod
echo.
echo ========================================
echo 构建前端项目 (生产环境)...
echo ========================================
if not exist %FRONTEND_DIR% (
    echo 错误: %FRONTEND_DIR% 目录不存在！
    pause
    goto exit
)

cd %FRONTEND_DIR%

echo 检查依赖...
if not exist node_modules (
    echo 正在安装依赖...
    call npm install
    if errorlevel 1 (
        echo 依赖安装失败！
        pause
        cd ..
        goto exit
    )
)

echo 正在构建前端项目 (生产环境)...
call npm run build:prod

if errorlevel 1 (
    echo 前端构建失败！
    pause
    cd ..
    goto exit
)

echo 前端构建成功！
echo 构建文件位于: %FRONTEND_DIR%\dist
cd ..
pause
goto exit

:build_frontend_stage
echo.
echo ========================================
echo 构建前端项目 (预发布环境)...
echo ========================================
if not exist %FRONTEND_DIR% (
    echo 错误: %FRONTEND_DIR% 目录不存在！
    pause
    goto exit
)

cd %FRONTEND_DIR%

echo 检查依赖...
if not exist node_modules (
    echo 正在安装依赖...
    call npm install
    if errorlevel 1 (
        echo 依赖安装失败！
        pause
        cd ..
        goto exit
    )
)

echo 正在构建前端项目 (预发布环境)...
call npm run build:stage

if errorlevel 1 (
    echo 前端构建失败！
    pause
    cd ..
    goto exit
)

echo 前端构建成功！
echo 构建文件位于: %FRONTEND_DIR%\dist
cd ..
pause
goto exit

:build_all
echo.
echo ========================================
echo 构建全部项目 (前端 + 后端)...
echo ========================================

echo [1/2] 构建后端项目...
echo 正在执行 Maven 构建...
call mvn clean package -Dmaven.test.skip=true

if errorlevel 1 (
    echo 后端构建失败！
    pause
    goto exit
)

if exist ruoyi-admin\target\ruoyi-admin.jar (
    copy ruoyi-admin\target\ruoyi-admin.jar . >nul
    echo 后端构建成功！JAR文件已复制到根目录
) else (
    echo 后端构建失败！未找到JAR文件
    pause
    goto exit
)

echo.
echo [2/2] 构建前端项目...
if not exist %FRONTEND_DIR% (
    echo 错误: %FRONTEND_DIR% 目录不存在！
    pause
    goto exit
)

cd %FRONTEND_DIR%

echo 检查依赖...
if not exist node_modules (
    echo 正在安装依赖...
    call npm install
    if errorlevel 1 (
        echo 依赖安装失败！
        pause
        cd ..
        goto exit
    )
)

echo 正在构建前端项目 (生产环境)...
call npm run build:prod

if errorlevel 1 (
    echo 前端构建失败！
    pause
    cd ..
    goto exit
)

echo 前端构建成功！
echo 构建文件位于: %FRONTEND_DIR%\dist
cd ..

echo.
echo ========================================
echo 全部项目构建完成！
echo ========================================
echo 后端JAR文件: %BACKEND_JAR%
echo 前端构建目录: %FRONTEND_DIR%\dist
pause
goto exit

:exit
echo.
echo 感谢使用汇易达保单监控系统！
exit /b 0
